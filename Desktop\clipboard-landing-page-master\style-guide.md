# Front-end Style Guide

## Layout

The designs were created to the following widths:

- Mobile: 375px
- Desktop: 1440px

> 💡 These are just the design sizes. Ensure content is responsive and meets WCAG requirements by testing the full range of screen sizes from 320px to large screens.

## Colors

### Primary

- Green 500: hsl(171, 66%, 44%)
- Blue 100: hsl(233, 100%, 69%)

### Neutral

- Gray 700: hsl(210, 10%, 33%)
- Gray 500: hsl(201, 11%, 66%)

## Typography

### Body Copy

- Font size: 18px

### Fonts

- Family: [<PERSON>juree](https://fonts.google.com/specimen/Bai+Jamjuree)
- Weights: 400, 600

## Icons

For the social icons, you can use the SVGs provided or a font icon library. Some font icon library suggestions can be found below:

- [Font Awesome](https://fontawesome.com)
- [IcoMoon](https://icomoon.io)
- [Ionicons](https://ionicons.com)

> 💎 [Upgrade to Pro](https://www.frontendmentor.io/pro?ref=style-guide) for design file access to see all design details and get hands-on experience using a professional workflow with tools like Figma.
