/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: '<PERSON>', sans-serif;
  font-size: 18px;
  line-height: 1.6;
  color: hsl(210, 10%, 33%);
  background-color: #fff;
}

.container {
  max-width: 1110px;
  margin: 0 auto;
  padding: 0 24px;
}

/* Typography */
h1, h2, h3 {
  font-weight: 600;
  color: hsl(210, 10%, 33%);
}

.hero-title {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  text-align: center;
}

.section-title {
  font-size: 2rem;
  margin-bottom: 1rem;
  text-align: center;
}

.hero-description,
.section-description {
  color: hsl(201, 11%, 66%);
  text-align: center;
  max-width: 730px;
  margin: 0 auto 3rem;
}

/* Buttons */
.download-buttons {
  display: flex;
  flex-direction: row;
  gap: 1rem;
  justify-content: center;
  align-items: center;
  margin-bottom: 3rem;
}

.btn {
  display: inline-block;
  padding: 1rem 2rem;
  border-radius: 28px;
  text-decoration: none;
  font-weight: 600;
  color: white;
  text-align: center;
  min-width: 227px;
  transition: all 0.3s ease;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.btn-primary {
  background-color: hsl(171, 66%, 44%);
  border-bottom: 3px solid hsl(171, 66%, 35%);
}

.btn-primary:hover {
  background-color: hsl(171, 66%, 50%);
  transform: translateY(-2px);
  box-shadow: 0 15px 25px rgba(0, 0, 0, 0.15);
}

.btn-secondary {
  background-color: hsl(233, 100%, 69%);
  border-bottom: 3px solid hsl(233, 100%, 60%);
}

.btn-secondary:hover {
  background-color: hsl(233, 100%, 75%);
  transform: translateY(-2px);
  box-shadow: 0 15px 25px rgba(0, 0, 0, 0.15);
}

/* Header */
.header {
  background-image: url('./images/bg-header-desktop.png');
  background-repeat: no-repeat;
  background-position: top center;
  background-size: cover;
  padding: 8rem 0 6rem;
  text-align: center;
}

.logo {
  width: 125px;
  height: 125px;
  margin-bottom: 3rem;
}

/* Sections */
section {
  padding: 6rem 0;
}

/* Features Section */
.features-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  margin-top: 4rem;
}

.features-image img {
  width: 100%;
  max-width: 752px;
  height: auto;
}

.features-list {
  padding-left: 2rem;
}

.feature-item {
  margin-bottom: 3rem;
}

.feature-item h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.feature-item p {
  color: hsl(201, 11%, 66%);
  line-height: 1.8;
}

/* Access Section */
.access {
  text-align: center;
}

.access-image {
  margin-top: 4rem;
}

.access-image img {
  width: 100%;
  max-width: 905px;
  height: auto;
}

/* Workflow Section */
.workflow {
  text-align: center;
}

.workflow-features {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  margin-top: 4rem;
}

.workflow-item {
  text-align: center;
}

.workflow-icon {
  width: 44px;
  height: 40px;
  margin-bottom: 2rem;
}

.workflow-item h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.workflow-item p {
  color: hsl(201, 11%, 66%);
  line-height: 1.8;
}

/* Companies Section */
.companies {
  padding: 4rem 0;
}

.companies-logos {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 2rem;
}

.companies-logos img {
  height: 72px;
  width: auto;
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.companies-logos img:hover {
  opacity: 1;
}

/* CTA Section */
.cta {
  text-align: center;
  padding: 6rem 0;
}

/* Footer */
.footer {
  background-color: #f5f6fa;
  padding: 3rem 0 1rem;
}

.footer-content {
  display: grid;
  grid-template-columns: auto 1fr auto;
  gap: 4rem;
  align-items: start;
  margin-bottom: 2rem;
}

.footer-logo img {
  width: 55px;
  height: 55px;
}

.footer-nav {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
}

.footer-nav-column {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.footer-nav a {
  color: hsl(210, 10%, 33%);
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-nav a:hover {
  color: hsl(171, 66%, 44%);
}

.social-links {
  display: flex;
  gap: 1.5rem;
}

.social-links a {
  transition: transform 0.3s ease;
}

.social-links a:hover {
  transform: scale(1.1);
}

.social-links img {
  width: 24px;
  height: 24px;
}

.attribution {
  font-size: 11px;
  text-align: center;
  color: hsl(201, 11%, 66%);
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e0e0e0;
}

.attribution a {
  color: hsl(228, 45%, 44%);
  text-decoration: none;
}

.attribution a:hover {
  text-decoration: underline;
}

/* Mobile Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 0 32px;
  }

  .hero-title {
    font-size: 2rem;
  }

  .section-title {
    font-size: 1.75rem;
  }

  .header {
    background-image: url('./images/bg-header-mobile.png');
    padding: 6rem 0 4rem;
  }

  .download-buttons {
    flex-direction: column;
    width: 100%;
  }

  .btn {
    width: 100%;
    max-width: 311px;
  }

  /* Features Section Mobile */
  .features-content {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }

  .features-list {
    padding-left: 0;
  }

  .feature-item {
    margin-bottom: 2rem;
  }

  /* Workflow Section Mobile */
  .workflow-features {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  /* Companies Section Mobile */
  .companies-logos {
    flex-direction: column;
    align-items: center;
    gap: 3rem;
  }

  .companies-logos img {
    height: 60px;
  }

  /* Footer Mobile */
  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 2rem;
  }

  .footer-nav {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .footer-nav-column {
    align-items: center;
  }

  .social-links {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 24px;
  }

  .hero-title {
    font-size: 1.75rem;
  }

  .section-title {
    font-size: 1.5rem;
  }

  .header {
    padding: 5rem 0 3rem;
  }

  .logo {
    width: 100px;
    height: 100px;
    margin-bottom: 2rem;
  }

  section {
    padding: 4rem 0;
  }

  .workflow-icon {
    width: 36px;
    height: 32px;
  }
}
